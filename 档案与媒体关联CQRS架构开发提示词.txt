================================================================================
                档案与媒体关联CQRS架构开发任务提示词
================================================================================

# 角色定义
你是一个专业的Golang后端架构师，精通CQRS架构模式、事件驱动设计、领域驱动设计(DDD)和微服务架构。
你需要根据以下需求使用CQRS架构模式开发档案与媒体关联功能。

# 任务背景
在数字证据管理系统中，需要实现档案(Archives)与媒体(Media)之间的关联关系管理。
系统要求采用CQRS(Command Query Responsibility Segregation)架构模式，实现读写分离、
事件驱动的高性能、高扩展性解决方案。

================================================================================
# CQRS架构核心原则
================================================================================

## 1. 命令查询职责分离
- **命令端(Command Side)**：处理写操作，关注业务逻辑和数据一致性
- **查询端(Query Side)**：处理读操作，关注查询性能和数据展示
- **事件驱动**：通过领域事件实现命令端和查询端的数据同步

## 2. 领域驱动设计
- **聚合根(Aggregate Root)**：Archives、Media、ArchivesMediaRelation
- **值对象(Value Object)**：RelationType、RelationStatus、AuditInfo
- **领域事件(Domain Event)**：关联创建、删除、状态变更事件
- **领域服务(Domain Service)**：复杂业务规则处理

## 3. 事件溯源(可选)
- **事件存储**：记录所有业务事件的完整历史
- **事件重放**：通过事件重放重建聚合状态
- **快照机制**：优化事件重放性能

================================================================================
# 核心需求
================================================================================

## 1. 领域模型设计

### 1.1 聚合根：ArchivesMediaRelation
```go
type ArchivesMediaRelation struct {
    // 聚合根标识
    ID int64 `json:"id" gorm:"primaryKey;autoIncrement"`

    // 业务标识
    ArchivesID int64 `json:"archives_id" gorm:"column:archives_id;not null;index"`
    MediaID    int64 `json:"media_id" gorm:"column:media_id;not null;index"`

    // 业务属性
    RelationType RelationType    `json:"relation_type" gorm:"column:relation_type;not null"`
    Priority     int            `json:"priority" gorm:"column:priority;default:1"`
    Description  string         `json:"description" gorm:"column:description;size:1000"`
    Tags         []string       `json:"tags" gorm:"-"` // 存储为JSON字符串
    TagsJSON     string         `json:"-" gorm:"column:tags;type:text"`

    // 业务状态
    Status       RelationStatus `json:"status" gorm:"column:status;not null;default:'active'"`
    ValidFrom    time.Time      `json:"valid_from" gorm:"column:valid_from;not null"`
    ValidTo      *time.Time     `json:"valid_to" gorm:"column:valid_to"`

    // 审计信息
    AuditInfo    AuditInfo      `json:"audit_info" gorm:"embedded"`

    // 聚合版本(并发控制)
    Version      int            `json:"version" gorm:"column:version;default:1"`

    // 未提交的领域事件
    uncommittedEvents []DomainEvent `gorm:"-"`
}

// 业务行为方法
func (r *ArchivesMediaRelation) Activate(userID int64) error
func (r *ArchivesMediaRelation) Deactivate(userID int64, reason string) error
func (r *ArchivesMediaRelation) UpdatePriority(priority int, userID int64) error
func (r *ArchivesMediaRelation) AddTag(tag string, userID int64) error
func (r *ArchivesMediaRelation) IsValid() bool
```

### 1.2 值对象定义
```go
// 关联类型
type RelationType string
const (
    RelationTypePrimary   RelationType = "primary"    // 主要证据
    RelationTypeSecondary RelationType = "secondary"  // 辅助证据
    RelationTypeReference RelationType = "reference"  // 参考资料
)

// 关联状态
type RelationStatus string
const (
    RelationStatusDraft    RelationStatus = "draft"    // 草稿
    RelationStatusPending  RelationStatus = "pending"  // 待审核
    RelationStatusActive   RelationStatus = "active"   // 有效
    RelationStatusInactive RelationStatus = "inactive" // 无效
)

// 审计信息
type AuditInfo struct {
    CreatedAt time.Time `json:"created_at" gorm:"column:created_at;autoCreateTime"`
    CreatedBy int64     `json:"created_by" gorm:"column:created_by;not null"`
    UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
    UpdatedBy int64     `json:"updated_by" gorm:"column:updated_by;not null"`
}
```

### 1.3 领域事件定义
```go
type DomainEvent interface {
    EventID() int64
    EventType() string
    AggregateID() int64
    OccurredAt() time.Time
    EventVersion() int
}

// 关联创建事件
type ArchivesMediaRelationCreatedEvent struct {
    ID           int64         `json:"id"`
    RelationID   int64         `json:"relation_id"`
    ArchivesID   int64         `json:"archives_id"`
    MediaID      int64         `json:"media_id"`
    RelationType RelationType  `json:"relation_type"`
    CreatedBy    int64         `json:"created_by"`
    OccurredAt   time.Time     `json:"occurred_at"`
    Version      int           `json:"version"`
}

// 关联状态变更事件
type ArchivesMediaRelationStatusChangedEvent struct {
    ID         int64          `json:"id"`
    RelationID int64          `json:"relation_id"`
    OldStatus  RelationStatus `json:"old_status"`
    NewStatus  RelationStatus `json:"new_status"`
    Reason     string         `json:"reason"`
    ChangedBy  int64          `json:"changed_by"`
    OccurredAt time.Time      `json:"occurred_at"`
    Version    int            `json:"version"`
}
```

## 2. 命令端架构设计

### 2.1 命令定义
```go
type Command interface {
    CommandID() int64
    CommandType() string
    AggregateID() int64
}

// 创建关联命令
type CreateArchivesMediaRelationCommand struct {
    ID           int64         `json:"id"`
    ArchivesID   int64         `json:"archives_id" validate:"required"`
    MediaID      int64         `json:"media_id" validate:"required"`
    RelationType RelationType  `json:"relation_type" validate:"required"`
    Description  string        `json:"description"`
    Priority     int           `json:"priority" validate:"min=1,max=10"`
    UserID       int64         `json:"user_id" validate:"required"`
}

// 批量创建关联命令
type CreateBatchArchivesMediaRelationCommand struct {
    ID         int64   `json:"id"`
    ArchivesID int64   `json:"archives_id" validate:"required"`
    MediaIDs   []int64 `json:"media_ids" validate:"required,min=1,max=100"`
    UserID     int64   `json:"user_id" validate:"required"`
}

// 删除关联命令
type DeleteArchivesMediaRelationCommand struct {
    ID         int64  `json:"id"`
    RelationID int64  `json:"relation_id" validate:"required"`
    Reason     string `json:"reason"`
    UserID     int64  `json:"user_id" validate:"required"`
}
```

### 2.2 命令处理器
```go
type CommandHandler interface {
    Handle(ctx context.Context, cmd Command) error
}

type CreateArchivesMediaRelationHandler struct {
    relationRepo    ArchivesMediaRelationRepository
    archivesRepo    ArchivesRepository
    mediaRepo       MediaRepository
    eventStore      EventStore
    validator       Validator
    logger          Logger
}

func (h *CreateArchivesMediaRelationHandler) Handle(ctx context.Context, cmd Command) error {
    createCmd := cmd.(*CreateArchivesMediaRelationCommand)

    // 1. 命令验证
    if err := h.validator.Validate(createCmd); err != nil {
        return fmt.Errorf("command validation failed: %w", err)
    }

    // 2. 业务规则验证
    if err := h.validateBusinessRules(ctx, createCmd); err != nil {
        return fmt.Errorf("business rule validation failed: %w", err)
    }

    // 3. 创建聚合
    relation, err := NewArchivesMediaRelation(
        createCmd.ArchivesID,
        createCmd.MediaID,
        createCmd.RelationType,
        createCmd.UserID,
    )
    if err != nil {
        return fmt.Errorf("failed to create relation: %w", err)
    }

    // 4. 保存聚合
    if err := h.relationRepo.Save(ctx, relation); err != nil {
        return fmt.Errorf("failed to save relation: %w", err)
    }

    // 5. 发布事件
    events := relation.GetUncommittedEvents()
    for _, event := range events {
        if err := h.eventStore.SaveEvent(ctx, event); err != nil {
            return fmt.Errorf("failed to save event: %w", err)
        }
    }

    relation.MarkEventsAsCommitted()
    return nil
}
```

### 2.3 仓储接口(命令端)
```go
type ArchivesMediaRelationRepository interface {
    Save(ctx context.Context, relation *ArchivesMediaRelation) error
    FindByID(ctx context.Context, id int64) (*ArchivesMediaRelation, error)
    FindByArchivesAndMedia(ctx context.Context, archivesID, mediaID int64) (*ArchivesMediaRelation, error)
    Delete(ctx context.Context, id int64) error
}

type EventStore interface {
    SaveEvent(ctx context.Context, event DomainEvent) error
    GetEvents(ctx context.Context, aggregateID int64) ([]DomainEvent, error)
    GetEventsAfterVersion(ctx context.Context, aggregateID int64, version int) ([]DomainEvent, error)
}
```

## 3. 查询端架构设计

### 3.1 读模型定义
```go
// 档案媒体关联列表读模型
type ArchivesMediaRelationListReadModel struct {
    ID                int64     `json:"id" gorm:"primaryKey;autoIncrement"`
    RelationID        int64     `json:"relation_id" gorm:"column:relation_id;index"`
    ArchivesID        int64     `json:"archives_id" gorm:"column:archives_id;index"`
    ArchivesCode      string    `json:"archives_code" gorm:"column:archives_code"`
    ArchivesTitle     string    `json:"archives_title" gorm:"column:archives_title"`
    MediaID           int64     `json:"media_id" gorm:"column:media_id;index"`
    MediaName         string    `json:"media_name" gorm:"column:media_name"`
    MediaType         string    `json:"media_type" gorm:"column:media_type"`
    MediaSize         int64     `json:"media_size" gorm:"column:media_size"`
    Duration          int64     `json:"duration" gorm:"column:duration"`
    RelationType      string    `json:"relation_type" gorm:"column:relation_type"`
    RelationStatus    string    `json:"relation_status" gorm:"column:relation_status"`
    Priority          int       `json:"priority" gorm:"column:priority"`
    Description       string    `json:"description" gorm:"column:description"`
    Tags              string    `json:"tags" gorm:"column:tags"` // JSON字符串
    CreatedAt         time.Time `json:"created_at" gorm:"column:created_at;index"`
    CreatedByID       int64     `json:"created_by_id" gorm:"column:created_by_id"`
    CreatedByName     string    `json:"created_by_name" gorm:"column:created_by_name"`
    UpdatedAt         time.Time `json:"updated_at" gorm:"column:updated_at"`
    UpdatedByID       int64     `json:"updated_by_id" gorm:"column:updated_by_id"`
    UpdatedByName     string    `json:"updated_by_name" gorm:"column:updated_by_name"`
}

// 档案媒体关联详情读模型
type ArchivesMediaRelationDetailReadModel struct {
    ArchivesMediaRelationListReadModel

    // 档案详细信息
    ArchivesDescription string    `json:"archives_description"`
    ArchivesCreatedAt   time.Time `json:"archives_created_at"`
    ArchivesStatus      string    `json:"archives_status"`

    // 媒体详细信息
    MediaDescription    string    `json:"media_description"`
    MediaPath          string    `json:"media_path"`
    MediaThumbnail     string    `json:"media_thumbnail"`
    MediaCreatedAt     time.Time `json:"media_created_at"`
    MediaStatus        string    `json:"media_status"`

    // 关联历史记录
    OperationHistory   []OperationRecord `json:"operation_history"`
}

type OperationRecord struct {
    ID          int64     `json:"id"`
    Operation   string    `json:"operation"`
    Description string    `json:"description"`
    OperatedAt  time.Time `json:"operated_at"`
    OperatedBy  string    `json:"operated_by"`
}
```

### 3.2 查询定义
```go
type Query interface {
    QueryID() int64
    QueryType() string
}

// 查询档案关联的媒体列表
type GetArchivesMediaListQuery struct {
    ID         int64                  `json:"id"`
    ArchivesID int64                  `json:"archives_id" validate:"required"`
    Filter     *ArchivesMediaFilter   `json:"filter"`
    Pagination *PaginationRequest     `json:"pagination"`
    Sorting    *SortingRequest        `json:"sorting"`
}

// 查询过滤条件
type ArchivesMediaFilter struct {
    RelationType   []RelationType   `json:"relation_type"`
    RelationStatus []RelationStatus `json:"relation_status"`
    MediaType      []string         `json:"media_type"`
    Tags           []string         `json:"tags"`
    CreatedBy      []int64          `json:"created_by"`
    DateRange      *DateRange       `json:"date_range"`
    Priority       *PriorityRange   `json:"priority"`
}

// 分页请求
type PaginationRequest struct {
    Page int `json:"page" validate:"min=1"`
    Size int `json:"size" validate:"min=1,max=100"`
}

// 排序请求
type SortingRequest struct {
    Field string `json:"field" validate:"required"`
    Order string `json:"order" validate:"oneof=asc desc"`
}

// 查询媒体关联的档案列表
type GetMediaArchivesListQuery struct {
    ID         int64                  `json:"id"`
    MediaID    int64                  `json:"media_id" validate:"required"`
    Filter     *MediaArchivesFilter   `json:"filter"`
    Pagination *PaginationRequest     `json:"pagination"`
    Sorting    *SortingRequest        `json:"sorting"`
}

// 查询关联详情
type GetArchivesMediaDetailQuery struct {
    ID         int64 `json:"id"`
    RelationID int64 `json:"relation_id" validate:"required"`
}
```

### 3.3 查询处理器
```go
type QueryHandler interface {
    Handle(ctx context.Context, query Query) (interface{}, error)
}

type GetArchivesMediaListHandler struct {
    readStore ArchivesMediaReadModelStore
    logger    Logger
}

func (h *GetArchivesMediaListHandler) Handle(ctx context.Context, query Query) (interface{}, error) {
    listQuery := query.(*GetArchivesMediaListQuery)

    // 1. 查询验证
    if err := h.validator.Validate(listQuery); err != nil {
        return nil, fmt.Errorf("query validation failed: %w", err)
    }

    // 2. 执行查询
    result, err := h.readStore.FindByArchivesID(ctx, listQuery.ArchivesID, listQuery.Filter, listQuery.Pagination, listQuery.Sorting)
    if err != nil {
        return nil, fmt.Errorf("failed to query archives media list: %w", err)
    }

    return result, nil
}

type GetArchivesMediaDetailHandler struct {
    readStore ArchivesMediaReadModelStore
    logger    Logger
}

func (h *GetArchivesMediaDetailHandler) Handle(ctx context.Context, query Query) (interface{}, error) {
    detailQuery := query.(*GetArchivesMediaDetailQuery)

    // 执行查询
    detail, err := h.readStore.FindDetailByRelationID(ctx, detailQuery.RelationID)
    if err != nil {
        return nil, fmt.Errorf("failed to query archives media detail: %w", err)
    }

    return detail, nil
}
```

### 3.4 读模型存储接口
```go
type ArchivesMediaReadModelStore interface {
    // 列表查询
    FindByArchivesID(ctx context.Context, archivesID int64, filter *ArchivesMediaFilter, pagination *PaginationRequest, sorting *SortingRequest) (*PaginatedArchivesMediaList, error)
    FindByMediaID(ctx context.Context, mediaID int64, filter *MediaArchivesFilter, pagination *PaginationRequest, sorting *SortingRequest) (*PaginatedMediaArchivesList, error)

    // 详情查询
    FindDetailByRelationID(ctx context.Context, relationID int64) (*ArchivesMediaRelationDetailReadModel, error)

    // 统计查询
    CountByArchivesID(ctx context.Context, archivesID int64, filter *ArchivesMediaFilter) (int64, error)
    CountByMediaID(ctx context.Context, mediaID int64, filter *MediaArchivesFilter) (int64, error)

    // 读模型更新
    UpsertListReadModel(ctx context.Context, model *ArchivesMediaRelationListReadModel) error
    UpsertDetailReadModel(ctx context.Context, model *ArchivesMediaRelationDetailReadModel) error
    DeleteByRelationID(ctx context.Context, relationID int64) error

    // 批量操作
    BatchUpsert(ctx context.Context, models []*ArchivesMediaRelationListReadModel) error
    BatchDelete(ctx context.Context, relationIDs []int64) error
}

// 分页结果
type PaginatedArchivesMediaList struct {
    Items      []*ArchivesMediaRelationListReadModel `json:"items"`
    Total      int64                                 `json:"total"`
    Page       int                                   `json:"page"`
    Size       int                                   `json:"size"`
    TotalPages int                                   `json:"total_pages"`
}
```

## 4. 事件处理架构

### 4.1 事件处理器
```go
type EventHandler interface {
    Handle(ctx context.Context, event DomainEvent) error
    EventType() string
}

// 读模型更新事件处理器
type ArchivesMediaRelationReadModelUpdater struct {
    readStore    ArchivesMediaReadModelStore
    archivesRepo ArchivesQueryRepository
    mediaRepo    MediaQueryRepository
    userRepo     UserQueryRepository
    logger       Logger
}

func (u *ArchivesMediaRelationReadModelUpdater) Handle(ctx context.Context, event DomainEvent) error {
    switch e := event.(type) {
    case *ArchivesMediaRelationCreatedEvent:
        return u.handleRelationCreated(ctx, e)
    case *ArchivesMediaRelationStatusChangedEvent:
        return u.handleStatusChanged(ctx, e)
    case *ArchivesMediaRelationDeletedEvent:
        return u.handleRelationDeleted(ctx, e)
    default:
        return fmt.Errorf("unsupported event type: %T", event)
    }
}

func (u *ArchivesMediaRelationReadModelUpdater) handleRelationCreated(ctx context.Context, event *ArchivesMediaRelationCreatedEvent) error {
    // 1. 获取相关数据
    archives, err := u.archivesRepo.FindByID(ctx, event.ArchivesID)
    if err != nil {
        return fmt.Errorf("failed to find archives: %w", err)
    }

    media, err := u.mediaRepo.FindByID(ctx, event.MediaID)
    if err != nil {
        return fmt.Errorf("failed to find media: %w", err)
    }

    user, err := u.userRepo.FindByID(ctx, event.CreatedBy)
    if err != nil {
        return fmt.Errorf("failed to find user: %w", err)
    }

    // 2. 构建读模型
    listReadModel := &ArchivesMediaRelationListReadModel{
        ID:              uuid.New(),
        RelationID:      event.RelationID,
        ArchivesID:      event.ArchivesID,
        ArchivesCode:    archives.Code,
        ArchivesTitle:   archives.Title,
        MediaID:         event.MediaID,
        MediaName:       media.Name,
        MediaType:       media.Type,
        MediaSize:       media.Size,
        Duration:        media.Duration,
        RelationType:    string(event.RelationType),
        RelationStatus:  string(RelationStatusActive),
        CreatedAt:       event.OccurredAt,
        CreatedByID:     event.CreatedBy,
        CreatedByName:   user.Name,
        UpdatedAt:       event.OccurredAt,
        UpdatedByID:     event.CreatedBy,
        UpdatedByName:   user.Name,
    }

    // 3. 保存读模型
    return u.readStore.UpsertListReadModel(ctx, listReadModel)
}
```

### 4.2 事件总线
```go
type EventBus interface {
    Publish(ctx context.Context, event DomainEvent) error
    Subscribe(eventType string, handler EventHandler) error
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
}

type InMemoryEventBus struct {
    handlers map[string][]EventHandler
    mutex    sync.RWMutex
    logger   Logger
}

func (bus *InMemoryEventBus) Publish(ctx context.Context, event DomainEvent) error {
    bus.mutex.RLock()
    handlers, exists := bus.handlers[event.EventType()]
    bus.mutex.RUnlock()

    if !exists {
        return nil // 没有处理器，忽略事件
    }

    // 并发处理事件
    var wg sync.WaitGroup
    errChan := make(chan error, len(handlers))

    for _, handler := range handlers {
        wg.Add(1)
        go func(h EventHandler) {
            defer wg.Done()
            if err := h.Handle(ctx, event); err != nil {
                errChan <- fmt.Errorf("handler %T failed: %w", h, err)
            }
        }(handler)
    }

    wg.Wait()
    close(errChan)

    // 收集错误
    var errors []error
    for err := range errChan {
        errors = append(errors, err)
    }

    if len(errors) > 0 {
        return fmt.Errorf("event handling failed: %v", errors)
    }

    return nil
}
```

## 5. API接口设计

### 5.1 命令API接口
```go
type ArchivesMediaCommandController struct {
    commandBus CommandBus
    validator  Validator
    logger     Logger
}

// 创建关联
func (c *ArchivesMediaCommandController) CreateRelation(ctx *gin.Context) {
    var req CreateArchivesMediaRelationRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_REQUEST",
            Message: "请求参数错误: " + err.Error(),
        })
        return
    }

    // 获取用户信息
    userID := getUserIDFromContext(ctx)

    // 构建命令
    cmd := &CreateArchivesMediaRelationCommand{
        ID:           generateID(), // 生成int64 ID
        ArchivesID:   req.ArchivesID,
        MediaID:      req.MediaID,
        RelationType: req.RelationType,
        Description:  req.Description,
        Priority:     req.Priority,
        UserID:       userID,
    }

    // 执行命令
    if err := c.commandBus.Send(ctx, cmd); err != nil {
        ctx.JSON(http.StatusInternalServerError, ErrorResponse{
            Code:    "COMMAND_FAILED",
            Message: "创建关联失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusCreated, SuccessResponse{
        Code:    "SUCCESS",
        Message: "关联创建成功",
        Data:    map[string]interface{}{"command_id": cmd.ID},
    })
}

// 批量创建关联
func (c *ArchivesMediaCommandController) CreateBatchRelations(ctx *gin.Context) {
    var req CreateBatchArchivesMediaRelationRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_REQUEST",
            Message: "请求参数错误: " + err.Error(),
        })
        return
    }

    userID := getUserIDFromContext(ctx)

    cmd := &CreateBatchArchivesMediaRelationCommand{
        ID:         generateID(), // 生成int64 ID
        ArchivesID: req.ArchivesID,
        MediaIDs:   req.MediaIDs,
        UserID:     userID,
    }

    if err := c.commandBus.Send(ctx, cmd); err != nil {
        ctx.JSON(http.StatusInternalServerError, ErrorResponse{
            Code:    "COMMAND_FAILED",
            Message: "批量创建关联失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusCreated, SuccessResponse{
        Code:    "SUCCESS",
        Message: "批量关联创建成功",
        Data:    map[string]interface{}{"command_id": cmd.ID},
    })
}

// 删除关联
func (c *ArchivesMediaCommandController) DeleteRelation(ctx *gin.Context) {
    var req DeleteArchivesMediaRelationRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_REQUEST",
            Message: "请求参数错误: " + err.Error(),
        })
        return
    }

    userID := getUserIDFromContext(ctx)

    cmd := &DeleteArchivesMediaRelationCommand{
        ID:         generateID(), // 生成int64 ID
        RelationID: req.RelationID,
        Reason:     req.Reason,
        UserID:     userID,
    }

    if err := c.commandBus.Send(ctx, cmd); err != nil {
        ctx.JSON(http.StatusInternalServerError, ErrorResponse{
            Code:    "COMMAND_FAILED",
            Message: "删除关联失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, SuccessResponse{
        Code:    "SUCCESS",
        Message: "关联删除成功",
        Data:    map[string]interface{}{"command_id": cmd.ID},
    })
}
```

### 5.2 查询API接口
```go
type ArchivesMediaQueryController struct {
    queryBus  QueryBus
    validator Validator
    logger    Logger
}

// 查询档案关联的媒体列表
func (c *ArchivesMediaQueryController) GetArchivesMediaList(ctx *gin.Context) {
    archivesIDStr := ctx.Param("archives_id")
    archivesID, err := strconv.ParseInt(archivesIDStr, 10, 64)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_ARCHIVES_ID",
            Message: "档案ID格式错误",
        })
        return
    }

    // 解析查询参数
    var filter ArchivesMediaFilter
    var pagination PaginationRequest
    var sorting SortingRequest

    if err := ctx.ShouldBindQuery(&filter); err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_FILTER",
            Message: "过滤条件错误: " + err.Error(),
        })
        return
    }

    if err := ctx.ShouldBindQuery(&pagination); err != nil {
        // 设置默认分页参数
        pagination = PaginationRequest{Page: 1, Size: 20}
    }

    if err := ctx.ShouldBindQuery(&sorting); err != nil {
        // 设置默认排序
        sorting = SortingRequest{Field: "created_at", Order: "desc"}
    }

    // 构建查询
    query := &GetArchivesMediaListQuery{
        ID:         generateID(), // 生成int64 ID
        ArchivesID: archivesID,
        Filter:     &filter,
        Pagination: &pagination,
        Sorting:    &sorting,
    }

    // 执行查询
    result, err := c.queryBus.Send(ctx, query)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, ErrorResponse{
            Code:    "QUERY_FAILED",
            Message: "查询失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, SuccessResponse{
        Code:    "SUCCESS",
        Message: "查询成功",
        Data:    result,
    })
}

// 查询关联详情
func (c *ArchivesMediaQueryController) GetArchivesMediaDetail(ctx *gin.Context) {
    relationIDStr := ctx.Param("relation_id")
    relationID, err := strconv.ParseInt(relationIDStr, 10, 64)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, ErrorResponse{
            Code:    "INVALID_RELATION_ID",
            Message: "关联ID格式错误",
        })
        return
    }

    query := &GetArchivesMediaDetailQuery{
        ID:         generateID(), // 生成int64 ID
        RelationID: relationID,
    }

    result, err := c.queryBus.Send(ctx, query)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, ErrorResponse{
            Code:    "QUERY_FAILED",
            Message: "查询失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, SuccessResponse{
        Code:    "SUCCESS",
        Message: "查询成功",
        Data:    result,
    })
}
```

### 5.3 路由注册
```go
func RegisterArchivesMediaRoutes(router *gin.RouterGroup, commandController *ArchivesMediaCommandController, queryController *ArchivesMediaQueryController) {
    // 命令路由
    commandGroup := router.Group("/archives-media/commands")
    {
        commandGroup.POST("/relations", commandController.CreateRelation)
        commandGroup.POST("/relations/batch", commandController.CreateBatchRelations)
        commandGroup.DELETE("/relations", commandController.DeleteRelation)
        commandGroup.PUT("/relations/:relation_id/status", commandController.UpdateRelationStatus)
        commandGroup.PUT("/relations/:relation_id/priority", commandController.UpdateRelationPriority)
    }

    // 查询路由
    queryGroup := router.Group("/archives-media/queries")
    {
        queryGroup.GET("/archives/:archives_id/media", queryController.GetArchivesMediaList)
        queryGroup.GET("/media/:media_id/archives", queryController.GetMediaArchivesList)
        queryGroup.GET("/relations/:relation_id", queryController.GetArchivesMediaDetail)
        queryGroup.GET("/relations/:relation_id/history", queryController.GetRelationHistory)
    }
}
```

================================================================================
# 技术要求与约束
================================================================================

## 技术栈要求
1. **编程语言**：Go 1.19+
2. **Web框架**：Gin
3. **ORM框架**：GORM
4. **数据库**：MySQL 8.0+ / PostgreSQL 13+
5. **消息队列**：Redis Streams / Apache Kafka / RabbitMQ
6. **缓存**：Redis
7. **依赖注入**：Wire / Fx
8. **配置管理**：Viper
9. **日志**：Zap / Logrus
10. **监控**：Prometheus + Grafana
11. **ID生成**：Snowflake算法 / 数据库自增ID

## ID生成策略
```go
// ID生成器接口
type IDGenerator interface {
    GenerateID() int64
}

// Snowflake ID生成器实现
type SnowflakeIDGenerator struct {
    node *snowflake.Node
}

func NewSnowflakeIDGenerator(nodeID int64) (*SnowflakeIDGenerator, error) {
    node, err := snowflake.NewNode(nodeID)
    if err != nil {
        return nil, err
    }
    return &SnowflakeIDGenerator{node: node}, nil
}

func (g *SnowflakeIDGenerator) GenerateID() int64 {
    return g.node.Generate().Int64()
}

// 全局ID生成函数
func generateID() int64 {
    return globalIDGenerator.GenerateID()
}
```

## CQRS架构要求
1. **严格分离**：命令和查询完全分离，不能混用
2. **事件驱动**：所有状态变更必须通过事件传播
3. **最终一致性**：接受读写模型之间的最终一致性
4. **幂等性**：所有命令和事件处理必须支持幂等
5. **版本控制**：聚合和事件必须支持版本控制
6. **错误处理**：完善的错误处理和重试机制

## 性能要求
1. **命令处理**：单个命令处理时间 < 100ms
2. **查询响应**：单个查询响应时间 < 50ms
3. **事件处理**：事件处理延迟 < 1s
4. **并发支持**：支持1000+并发请求
5. **数据一致性**：最终一致性延迟 < 5s

## 数据完整性
1. **聚合一致性**：聚合内部强一致性
2. **事件顺序**：同一聚合的事件严格有序
3. **事务边界**：明确的事务边界定义
4. **补偿机制**：失败场景的补偿处理

================================================================================
# 测试要求
================================================================================

## 单元测试
1. **聚合测试**：测试聚合的业务逻辑和状态变更
2. **命令处理器测试**：测试命令处理的各种场景
3. **查询处理器测试**：测试查询逻辑和数据转换
4. **事件处理器测试**：测试事件处理和读模型更新
5. **覆盖率要求**：代码覆盖率 > 80%

## 集成测试
1. **端到端测试**：完整的命令-事件-查询流程测试
2. **并发测试**：多线程并发操作测试
3. **性能测试**：压力测试和性能基准测试
4. **容错测试**：故障注入和恢复测试

## 契约测试
1. **API契约测试**：前后端接口契约验证
2. **事件契约测试**：事件格式和版本兼容性测试
3. **数据库契约测试**：数据模型变更兼容性测试

================================================================================
# 交付物清单
================================================================================

## 领域层
- [ ] 聚合根定义 (ArchivesMediaRelation)
- [ ] 值对象定义 (RelationType, RelationStatus, AuditInfo)
- [ ] 领域事件定义 (所有业务事件)
- [ ] 领域服务定义 (复杂业务规则)
- [ ] 仓储接口定义 (命令端仓储)

## 应用层
- [ ] 命令定义 (所有业务命令)
- [ ] 命令处理器 (CommandHandler实现)
- [ ] 查询定义 (所有业务查询)
- [ ] 查询处理器 (QueryHandler实现)
- [ ] 应用服务 (协调层服务)

## 基础设施层
- [ ] 仓储实现 (GORM实现)
- [ ] 事件存储实现 (EventStore)
- [ ] 事件总线实现 (EventBus)
- [ ] 读模型存储实现 (ReadModelStore)
- [ ] 消息队列集成

## 接口层
- [ ] 命令API控制器
- [ ] 查询API控制器
- [ ] 请求/响应DTO定义
- [ ] 路由配置
- [ ] 中间件实现

## 配置和部署
- [ ] 应用配置文件
- [ ] 数据库迁移脚本
- [ ] Docker配置文件
- [ ] Kubernetes部署文件
- [ ] 监控配置

## 测试代码
- [ ] 单元测试套件
- [ ] 集成测试套件
- [ ] 性能测试脚本
- [ ] 测试数据准备脚本

================================================================================
# 开发优先级
================================================================================

## 第一阶段（核心架构）
1. 领域模型设计和实现
2. 基础的CQRS架构搭建
3. 事件存储和事件总线实现
4. 基本的命令和查询处理

## 第二阶段（功能完善）
1. 完整的业务逻辑实现
2. 读模型构建和优化
3. API接口开发
4. 错误处理和验证

## 第三阶段（性能优化）
1. 查询性能优化
2. 事件处理优化
3. 缓存策略实现
4. 监控和指标收集

## 第四阶段（生产就绪）
1. 完整的测试覆盖
2. 文档编写
3. 部署配置
4. 运维工具

================================================================================
# 注意事项
================================================================================

1. **架构原则**：严格遵循CQRS和DDD原则，不要为了便利而妥协架构
2. **事件设计**：事件应该表达业务意图，而不是技术实现细节
3. **聚合边界**：合理设计聚合边界，避免过大或过小的聚合
4. **性能考虑**：在保证架构清晰的前提下，关注性能优化
5. **错误处理**：完善的错误处理和补偿机制
6. **监控告警**：完整的监控指标和告警机制
7. **文档维护**：及时更新架构文档和API文档
8. **版本管理**：事件和API的版本兼容性管理

开始开发前，请确认你理解了CQRS架构的核心概念和实现要求。
开发过程中遇到架构设计问题，请参考DDD和CQRS的最佳实践。

================================================================================
```
```